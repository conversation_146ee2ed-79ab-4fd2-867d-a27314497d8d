const translation = {
  knowledge: 'ナレッジベース',
  chunkingMode: {
    general: '汎用',
    parentChild: '親子',
  },
  parentMode: {
    paragraph: '段落',
    fullDoc: '全体',
  },
  externalTag: '外部',
  externalAPI: '外部 API',
  externalAPIPanelTitle: '外部ナレッジベース連携 API',
  externalKnowledgeId: '外部ナレッジベース ID',
  externalKnowledgeName: '外部ナレッジベース名',
  externalKnowledgeDescription: 'ナレッジベースの説明',
  externalKnowledgeIdPlaceholder: 'ナレッジベース ID を入力',
  externalKnowledgeNamePlaceholder: 'ナレッジベース名を入力',
  externalKnowledgeDescriptionPlaceholder: 'このナレッジベースの説明（任意）',
  learnHowToWriteGoodKnowledgeDescription: '効果的なナレッジベースの説明の書き方',
  externalAPIPanelDescription: '外部ナレッジベース連携 API は、Dify 外のナレッジベースと連携し、そこからナレッジベースを取得するために使用します。',
  externalAPIPanelDocumentation: '外部ナレッジベース連携 API の作成方法',
  localDocs: 'ローカルドキュメント',
  documentCount: ' ドキュメント',
  wordCount: ' k 単語',
  appCount: ' リンクされたアプリ',
  createDataset: 'ナレッジベースを作成',
  createNewExternalAPI: '新しい外部ナレッジベース連携 API を作成',
  noExternalKnowledge: '外部ナレッジベース連携 API がありません。ここをクリックして作成してください',
  createExternalAPI: '外部ナレッジベース連携 API を追加',
  editExternalAPIFormTitle: '外部ナレッジベース連携 API を編集',
  editExternalAPITooltipTitle: '連携中のナレッジベース',
  editExternalAPIConfirmWarningContent: {
    front: 'この外部ナレッジベース連携 API は',
    end: '件の外部ナレッジベースと連携しており、この変更はすべてに適用されます。変更を保存しますか？',
  },
  editExternalAPIFormWarning: {
    front: 'この外部 API は',
    end: '件の外部ナレッジベースと連携しています',
  },
  deleteExternalAPIConfirmWarningContent: {
    title: {
      front: '削除',
      end: 'しますか？',
    },
    content: {
      front: 'この外部ナレッジベース連携 API は',
      end: '件の外部ナレッジベースと連携しています。この API を削除すると、すべて無効になります。この API を削除しますか？',
    },
    noConnectionContent: 'この API を削除しますか？',
  },
  selectExternalKnowledgeAPI: {
    placeholder: '外部ナレッジベース連携 API を選択',
  },
  connectDataset: '外部ナレッジベースと連携',
  connectDatasetIntro: {
    title: '外部ナレッジベースとの連携方法',
    content: {
      front: '外部ナレッジベースと連携するには、まず外部 API を作成する必要があります。以下の手順を参照し、',
      link: '外部 API の作成方法',
      end: 'をご確認ください。次に、対応するナレッジベース ID を左側のフォームに入力してください。すべての情報が正しければ、連携ボタンをクリックすると、自動的にナレッジベースの検索テストに移動します。',
    },
    learnMore: '詳細はこちら',
  },
  connectHelper: {
    helper1: 'API とナレッジベース ID を使って外部ナレッジベースと連携します。現在、',
    helper2: '検索機能のみがサポートされています。',
    helper3: 'この機能を使用する前に、',
    helper4: 'ヘルプドキュメント',
    helper5: 'をよくお読みください。',
  },
  createDatasetIntro: '独自のテキストデータをインポートするか、LLM コンテキストの強化のために Webhook を介してリアルタイムでデータを書き込むことができます。',
  deleteDatasetConfirmTitle: 'このナレッジベースを削除しますか？',
  deleteDatasetConfirmContent:
    'ナレッジベースを削除すると元に戻すことはできません。ユーザーはもはやあなたのナレッジベースにアクセスできず、すべてのプロンプトの設定とログが永久に削除されます。',
  datasetUsedByApp: 'このナレッジベースは一部のアプリによって使用されています。アプリはこのナレッジベースを使用できなくなり、すべてのプロンプト設定とログは永久に削除されます。',
  datasetDeleted: 'ナレッジベースが削除されました',
  datasetDeleteFailed: 'ナレッジベースの削除に失敗しました',
  didYouKnow: 'ご存知ですか？',
  intro1: 'ナレッジベースは Dify アプリケーションに統合することができます',
  intro2: 'コンテキストとして',
  intro3: '、',
  intro4: 'または',
  intro5: '作成することができます',
  intro6: '単体の ChatGPT インデックスプラグインとして公開するために',
  unavailable: '利用不可',
  unavailableTip: '埋め込みモデルが利用できません。デフォルトの埋め込みモデルを設定する必要があります',
  datasets: 'ナレッジベース',
  datasetsApi: 'API ACCESS',
  externalKnowledgeForm: {
    connect: '連携',
    cancel: 'キャンセル',
  },
  externalAPIForm: {
    name: '名前',
    endpoint: 'API エンドポイント',
    apiKey: 'API キー',
    save: '保存',
    cancel: 'キャンセル',
    edit: '編集',
    encrypted: {
      front: 'API トークンは',
      end: '技術で暗号化され、安全に保存されます。',
    },
  },
  retrieval: {
    semantic_search: {
      title: 'ベクトル検索',
      description: 'クエリの埋め込みを生成し、そのベクトル表現に最も類似したテキストチャンクを検索します。',
    },
    full_text_search: {
      title: '全文検索',
      description: 'ドキュメント内のすべての用語をインデックス化し、ユーザーが任意の用語を検索してそれに関連するテキストチャンクを取得できるようにします。',
    },
    hybrid_search: {
      title: 'ハイブリッド検索',
      description: '全文検索とベクトル検索を同時に実行し、ユーザーのクエリに最適なマッチを選択するために Rerank 付けを行います。Rerank モデル API の設定が必要です。',
      recommend: '推奨',
    },
    invertedIndex: {
      title: '転置インデックス',
      description: '効率的な検索に使用される構造です。各用語が含まれるドキュメントまたは Web ページを指すように、用語ごとに整理されています。',
    },
    change: '変更',
    changeRetrievalMethod: '検索方法の変更',
  },
  docsFailedNotice: 'ドキュメントのインデックス作成に失敗しました',
  retry: '再試行',
  documentsDisabled: '{{num}}件のドキュメントが無効 - 30 日以上非アクティブ',
  enable: '有効化',
  indexingTechnique: {
    high_quality: '高品質',
    economy: '経済',
  },
  indexingMethod: {
    semantic_search: 'ベクトル検索',
    full_text_search: 'フルテキスト検索',
    hybrid_search: 'ハイブリッド検索',
    invertedIndex: '転置',
  },
  defaultRetrievalTip: 'デフォルトでは、マルチパス検索が使用されます。複数のナレッジベースから情報を取得した後、再ランキングを行います。',
  mixtureHighQualityAndEconomicTip: '高品質なナレッジベースとコスト重視のナレッジベースを混在させるには、Rerank モデルが必要です。',
  inconsistentEmbeddingModelTip: '選択されたナレッジベースの埋め込みモデルに一貫性がない場合、Rerank モデルが必要です。',
  mixtureInternalAndExternalTip: '内部と外部のナレッジベースを混在させる場合、Rerank モデルが必要です。',
  allExternalTip: '外部ナレッジベースのみを使用する場合、Rerank モデルを有効にするかを選択できます。有効にしない場合、検索結果はスコアに基づいてソートされます。異なるナレッジベースで検索戦略が一貫していないと、結果が不正確になる可能性があります。',
  retrievalSettings: '検索設定',
  rerankSettings: 'Rerank 設定',
  weightedScore: {
    title: 'ウェイト設定',
    description: '重みを調整することで、並べ替え戦略はセマンティックマッチングとキーワードマッチングのどちらを優先するかを決定します。',
    semanticFirst: 'セマンティック優先',
    keywordFirst: 'キーワード優先',
    customized: 'カスタマイズ',
    semantic: 'セマンティクス',
    keyword: 'キーワード',
  },
  nTo1RetrievalLegacy: '製品計画によると、N-to-1 Retrieval は 9 月に正式に廃止される予定です。それまでは通常通り使用できます。',
  nTo1RetrievalLegacyLink: '詳細はこちら',
  nTo1RetrievalLegacyLinkText: ' N-to-1 retrieval は 9 月に正式に廃止されます。',
  batchAction: {
    selected: '選択済み',
    enable: '有効にする',
    disable: '無効にする',
    archive: 'アーカイブ',
    delete: '削除',
    cancel: 'キャンセル',
  },
  preprocessDocument: '{{num}}件のドキュメントを前処理',
  allKnowledge: 'ナレッジベース全体',
  allKnowledgeDescription: 'このワークスペースにナレッジベース全体を表示する場合に選択します。ワークスペースのオーナーのみがすべてのナレッジベースを管理できます。',
  embeddingModelNotAvailable: 'Embedding モデル不可用。',
  metadata: {
    metadata: 'メタデータ',
    addMetadata: 'メタデータを追加',
    chooseTime: '時間を選択',
    createMetadata: {
      title: '新規メタデータ',
      back: '戻る',
      type: 'タイプ',
      name: '名称',
      namePlaceholder: 'メタデータ名を入力',
    },
    checkName: {
      empty: 'メタデータ名を入力してください',
      invalid: 'メタデータ名は小文字、数字、アンダースコアのみを使用し、小文字で始める必要があります',
      tooLong: 'メタデータ名は {{max}} 文字を超えることはできません',
    },
    batchEditMetadata: {
      editMetadata: 'メタデータを編集',
      editDocumentsNum: '{{num}}件のドキュメントを編集',
      applyToAllSelectDocument: '選択したすべてのドキュメントに適用',
      applyToAllSelectDocumentTip: '上記の編集と新しいメタデータを選択したすべてのドキュメントに自動的に適用します。チェックしない場合、既にメタデータを持つドキュメントにのみ編集が適用されます。',
      multipleValue: '複数の値',
    },
    selectMetadata: {
      search: 'メタデータを検索',
      newAction: '新規メタデータ',
      manageAction: '管理',
    },
    datasetMetadata: {
      description: 'メタデータはドキュメントに関する情報で、ドキュメントの属性を説明するために使用されます。メタデータを活用することで、ドキュメントをより効率的に整理・管理できます。',
      addMetaData: 'メタデータを追加',
      values: '{{num}}個の値',
      disabled: '無効',
      rename: '名前変更',
      name: '名称',
      namePlaceholder: 'メタデータ名',
      builtIn: '組み込み',
      builtInDescription: '組み込みメタデータはシステムによって事前定義されたメタデータです。ここで組み込みメタデータの表示と管理ができます。',
      deleteTitle: '削除の確認',
      deleteContent: 'メタデータ「{{name}}」を削除してもよろしいですか？',
    },
    documentMetadata: {
      metadataToolTip: 'メタデータはドキュメントに関する情報で、ドキュメントの属性を説明するために使用されます。メタデータを活用することで、ドキュメントをより効率的に整理・管理できます。',
      startLabeling: 'ラベリングを開始',
      documentInformation: 'ドキュメント情報',
      technicalParameters: '技術パラメータ',
    },
  },
}

export default translation
