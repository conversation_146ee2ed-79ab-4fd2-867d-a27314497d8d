const translation = {
  knowledge: 'Bilgi',
  documentCount: ' belge',
  wordCount: ' k kelime',
  appCount: ' bağlı uygulamalar',
  createDataset: 'Bilgi Oluştur',
  createDatasetIntro: '<PERSON><PERSON> metin verilerinizi içe aktarın veya Webhook aracılığıyla gerçek zamanlı olarak veri yazın, LLM bağlamını geliştirin.',
  deleteDatasetConfirmTitle: 'Bu Bilgi\'yi silmek istiyor musunuz?',
  deleteDatasetConfirmContent:
    'Bilginin silinmesi geri alınamaz. Kullanıcılar artık Bilginize erişemeyecek ve tüm prompt yapılandırmaları ve günlükler kalıcı olarak silinecektir.',
  datasetUsedByApp: 'Bilgi bazı uygulamalar tarafından kullanılıyor. Uygulamalar artık bu Bilgiyi kullanamayacak ve tüm prompt yapılandırmaları ve günlükler kalıcı olarak silinecektir.',
  datasetDeleted: 'Bilgi silindi',
  datasetDeleteFailed: 'Bilgi silinemedi',
  didYouKnow: 'Biliyor muydunuz?',
  intro1: 'Bilgi, Dify uygulamasına ',
  intro2: 'bağlam olarak',
  intro3: ' entegre edilebilir,',
  intro4: 'veya ',
  intro5: 'bağımsız bir ChatGPT dizin eklentisi olarak oluşturulabilir',
  intro6: ' ve yayınlanabilir.',
  unavailable: 'Kullanılamıyor',
  unavailableTip: 'Yerleştirme modeli mevcut değil, varsayılan yerleştirme modelinin yapılandırılması gerekiyor',
  datasets: 'BİLGİ',
  datasetsApi: 'API ERİŞİMİ',
  retrieval: {
    semantic_search: {
      title: 'Vektör Arama',
      description: 'Sorgu yerleştirmelerini oluşturun ve vektör temsiline en benzeyen metin parçasını arayın.',
    },
    full_text_search: {
      title: 'Tam Metin Arama',
      description: 'Belgelerdeki tüm terimleri dizinleyerek, kullanıcıların herhangi bir terimi aramasına ve bu terimleri içeren ilgili metin parçasını geri almasına olanak tanır.',
    },
    hybrid_search: {
      title: 'Hibrit Arama',
      description: 'Tam metin arama ve vektör aramalarını aynı anda çalıştırın, kullanıcı sorgusu için en iyi eşleşmeyi seçmek için yeniden sıralayın. Kullanıcılar ağırlıklar ayarlayabilir veya bir Yeniden Sıralama modeli yapılandırabilir.',
      recommend: 'Önerilir',
    },
    invertedIndex: {
      title: 'Ters Dizine Kayıt',
      description: 'Ters Dizine Kayıt, verimli geri alım için kullanılan bir yapıdır. Terimlere göre düzenlenir ve her terim, onu içeren belgelere veya web sayfalarına işaret eder.',
    },
    change: 'Değiştir',
    changeRetrievalMethod: 'Geri alma yöntemini değiştir',
  },
  docsFailedNotice: 'belgeler dizine eklenemedi',
  retry: 'Yeniden Dene',
  indexingTechnique: {
    high_quality: 'Yüksek Kalite',
    economy: 'Ekonomi',
  },
  indexingMethod: {
    semantic_search: 'VEKTÖR',
    full_text_search: 'TAM METİN',
    hybrid_search: 'HİBRİT',
    invertedIndex: 'TERS',
  },
  mixtureHighQualityAndEconomicTip: 'Yüksek kaliteli ve ekonomik bilgi tabanlarının karışımı için Yeniden Sıralama modeli gereklidir.',
  inconsistentEmbeddingModelTip: 'Seçilen bilgi tabanlarının Yerleştirme modelleri tutarsızsa Yeniden Sıralama modeli gereklidir.',
  retrievalSettings: 'Geri Alım Ayarı',
  rerankSettings: 'Yeniden Sıralama Ayarı',
  weightedScore: {
    title: 'Ağırlıklı Puan',
    description: 'Verilen ağırlıkları ayarlayarak bu yeniden sıralama stratejisi, anlamsal mı yoksa anahtar kelime eşleştirmesini mi önceliklendireceğini belirler.',
    semanticFirst: 'Anlamsal Öncelikli',
    keywordFirst: 'Anahtar Kelime Öncelikli',
    customized: 'Özelleştirilmiş',
    semantic: 'Anlamsal',
    keyword: 'Anahtar Kelime',
  },
  nTo1RetrievalLegacy: 'Geri alım stratejisinin optimizasyonu ve yükseltilmesi nedeniyle, N-to-1 geri alımı Eylül ayında resmi olarak kullanım dışı kalacaktır. O zamana kadar normal şekilde kullanabilirsiniz.',
  nTo1RetrievalLegacyLink: 'Daha fazla bilgi edin',
  nTo1RetrievalLegacyLinkText: 'N-1 geri alma Eylül ayında resmi olarak kullanımdan kaldırılacaktır.',
  defaultRetrievalTip: 'Varsayılan olarak çok alma kullanılır. Bilgi, birden fazla bilgi tabanından alınır ve ardından yeniden sıralanır.',
  editExternalAPIConfirmWarningContent: {
    front: 'Bu Harici Bilgi API\'si aşağıdakilerle bağlantılıdır',
    end: 'Dışsal bilgi ve bu değişiklik hepsine uygulanacaktır. Bu değişikliği kaydetmek istediğinizden emin misiniz?',
  },
  editExternalAPIFormWarning: {
    end: 'Dış bilgi',
    front: 'Bu Harici API aşağıdakilere bağlıdır:',
  },
  deleteExternalAPIConfirmWarningContent: {
    title: {
      front: 'Silmek',
      end: '?',
    },
    content: {
      front: 'Bu Harici Bilgi API\'si aşağıdakilerle bağlantılıdır',
      end: 'dış bilgi. Bu API\'yi silmek hepsini geçersiz kılacaktır. Bu API\'yi silmek istediğinizden emin misiniz?',
    },
    noConnectionContent: 'Bu API\'yi sildiğinizden emin misiniz?',
  },
  selectExternalKnowledgeAPI: {
    placeholder: 'Bir Harici Bilgi API\'si seçin',
  },
  connectDatasetIntro: {
    content: {
      front: 'Harici bir bilgi bankasına bağlanmak için önce harici bir API oluşturmanız gerekir. Lütfen dikkatlice okuyun ve bakınız',
      end: '. Ardından ilgili bilgi kimliğini bulun ve soldaki forma doldurun. Tüm bilgiler doğruysa, bağlan düğmesine tıkladıktan sonra otomatik olarak bilgi tabanındaki alma testine atlayacaktır.',
      link: 'Harici API oluşturmayı öğrenin',
    },
    title: 'Harici bir bilgi bankasına nasıl bağlanılır',
    learnMore: 'Daha fazla bilgi edinin',
  },
  connectHelper: {
    helper2: 'Yalnızca alma işlevi desteklenir',
    helper5: 'Bu özelliği kullanmadan önce dikkatlice kullanın.',
    helper3: '. Şunları yapmanızı şiddetle tavsiye ederiz',
    helper1: 'API ve bilgi bankası kimliği aracılığıyla harici bilgi bankalarına bağlanın. Şu anda,',
    helper4: 'Yardım belgelerini okuyun',
  },
  externalKnowledgeForm: {
    connect: 'Bağlamak',
    cancel: 'İptal',
  },
  externalAPIForm: {
    encrypted: {
      end: 'Teknoloji.',
      front: 'API Token\'ınız kullanılarak şifrelenecek ve saklanacaktır.',
    },
    save: 'Kurtarmak',
    cancel: 'İptal',
    endpoint: 'API Uç Noktası',
    edit: 'Düzenlemek',
    name: 'Ad',
    apiKey: 'API Anahtarı',
  },
  externalTag: 'Dış',
  externalAPI: 'Harici API',
  externalKnowledgeDescription: 'Bilgi Açıklaması',
  externalAPIPanelDescription: 'Harici bilgi API\'si, Dify dışındaki bir bilgi bankasına bağlanmak ve bu bilgi bankasından bilgi almak için kullanılır.',
  externalKnowledgeDescriptionPlaceholder: 'Bu Bilgi Bankası\'nda neler olduğunu açıklayın (isteğe bağlı)',
  externalAPIPanelDocumentation: 'External Knowledge API\'nin nasıl oluşturulacağını öğrenin',
  mixtureInternalAndExternalTip: 'Rerank modeli, iç ve dış bilgilerin karışımı için gereklidir.',
  externalKnowledgeName: 'Dış Bilgi Adı',
  connectDataset: 'Harici bir bilgi bankasına bağlanın',
  editExternalAPITooltipTitle: 'BAĞLANTILI BILGI',
  externalAPIPanelTitle: 'Harici Bilgi API\'si',
  editExternalAPIFormTitle: 'External Knowledge API\'yi düzenleme',
  externalKnowledgeIdPlaceholder: 'Lütfen Bilgi Kimliğini girin',
  learnHowToWriteGoodKnowledgeDescription: 'İyi bir bilgi açıklamasının nasıl yazılacağını öğrenin',
  externalKnowledgeNamePlaceholder: 'Lütfen bilgi bankasının adını giriniz',
  noExternalKnowledge: 'Henüz Harici Bilgi API\'si yok, oluşturmak için buraya tıklayın',
  allExternalTip: 'Yalnızca harici bilgileri kullanırken, kullanıcı Rerank modelinin etkinleştirilip etkinleştirilmeyeceğini seçebilir. Etkinleştirilmezse, alınan parçalar puanlara göre sıralanır. Farklı bilgi tabanlarının erişim stratejileri tutarsız olduğunda, yanlış olacaktır.',
  externalKnowledgeId: 'Harici Bilgi Kimliği',
  createExternalAPI: 'Harici bilgi API\'si ekleme',
  createNewExternalAPI: 'Yeni bir External Knowledge API oluşturma',
  chunkingMode: {
    general: 'Genel',
    parentChild: 'Ebeveyn-çocuk',
  },
  parentMode: {
    fullDoc: 'Tam doküman',
    paragraph: 'Paragraf',
  },
  batchAction: {
    selected: 'Seçilmiş',
    cancel: 'İptal',
    enable: 'Etkinleştirmek',
    delete: 'Silmek',
    archive: 'Arşiv',
    disable: 'Devre dışı bırakmak',
  },
  preprocessDocument: '{{sayı}} Belgeleri Ön İşleme',
  localDocs: 'Yerel Dokümanlar',
  documentsDisabled: '{{num}} belge devre dışı - 30 günden uzun süre etkin değil',
  enable: 'Etkinleştirmek',
  allKnowledge: 'Tüm Bilgiler',
  allKnowledgeDescription: 'Bu çalışma alanındaki tüm bilgileri görüntülemek için seçin. Yalnızca Çalışma Alanı Sahibi tüm bilgileri yönetebilir.',
  metadata: {
    createMetadata: {
      namePlaceholder: 'Meta veri adı ekleyin',
      back: 'Geri',
      name: 'İsim',
      type: 'Yaz',
      title: 'Yeni Veriler',
    },
    checkName: {
      empty: 'Meta veri adı boş olamaz',
      invalid: 'Meta verisi adı yalnızca küçük harfler, sayılar ve alt çizgiler içerebilir ve küçük bir harfle başlamalıdır.',
      tooLong: 'Meta veri adı {{max}} karakteri geçemez',
    },
    batchEditMetadata: {
      multipleValue: 'Birden Fazla Değer',
      applyToAllSelectDocumentTip: 'Seçilen tüm belgeler için yukarıda düzenlenmiş ve yeni olan tüm meta verileri otomatik olarak oluşturun, aksi takdirde meta verileri düzenlemek yalnızca bununla ilgili belgelere uygulanacaktır.',
      editDocumentsNum: '{{num}} belge düzenleniyor',
      editMetadata: 'Meta Verileri Düzenle',
      applyToAllSelectDocument: 'Seçilen tüm belgelere uygula',
    },
    selectMetadata: {
      newAction: 'Yeni Veriler',
      manageAction: 'Yönet',
      search: 'Arama meta verileri',
    },
    datasetMetadata: {
      disabled: 'Devre dışı bırakıldı.',
      builtIn: 'Yerleşik',
      values: '{{num}} Değerler',
      builtInDescription: 'Yerleşik meta veriler otomatik olarak çıkarılır ve oluşturulur. Kullanımdan önce etkinleştirilmesi gerekir ve düzenlenemez.',
      rename: 'Yeniden Adlandır',
      addMetaData: 'Meta Verileri Ekle',
      name: 'İsim',
      deleteContent: 'Bu {{name}} meta verisini silmek istediğinizden emin misiniz?',
      namePlaceholder: 'Meta veri adı',
      deleteTitle: 'Silmek için onayla',
      description: 'Bu bilgideki tüm meta verileri yönetebilirsiniz. Değişiklikler her belgeye senkronize edilecektir.',
    },
    documentMetadata: {
      documentInformation: 'Belge Bilgisi',
      metadataToolTip: 'Meta veriler, bilgi alma doğruluğunu ve geçerliliğini artıran önemli bir filtre görevi görür. Bu belgede meta verileri burada değiştirebilir ve ekleyebilirsiniz.',
      startLabeling: 'Etiketlemeye Başla',
      technicalParameters: 'Teknik Parametreler',
    },
    metadata: 'Veri Seti',
    addMetadata: 'Meta Verileri Ekle',
    chooseTime: 'Bir zaman seçin...',
  },
  embeddingModelNotAvailable: 'Gömme modeli mevcut değil.',
}

export default translation
